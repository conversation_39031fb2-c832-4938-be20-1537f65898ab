<template>
  <el-drawer title="客户信息" :visible.sync="visible" size="60%" :destroy-on-close="true" class="drawer_box">
    <div class="detail-container" v-loading="loading">
      <div class="detail-section">
        <div class="detail-actions">
          <h3 class="section-title">客户信息</h3>
          <div>
            <el-button type="primary" @click="handleEdit" size="small">编辑</el-button>
            <el-button type="danger" @click="handleDelete" size="small">删除</el-button>
          </div>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">客户名称：</span>
              <span class="value">{{ detailInfo.customerName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">联系电话：</span>
              <span class="value">{{ detailInfo.customerPhone || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <span class="label">客户地址：</span>
              <span class="value">{{ detailInfo.customerAddress || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ detailInfo.createTime || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">更新时间：</span>
              <span class="value">{{ detailInfo.updateTime || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">创建人：</span>
              <span class="value">{{ detailInfo.createBy || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">更新人：</span>
              <span class="value">{{ detailInfo.updateBy || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="detail-section">
        <h3 class="section-title">备注信息</h3>
        <div class="remark-content">
          {{ detailInfo.remark || '-' }}
        </div>
      </div>


      <!-- 关联的下货任务 -->
      <div class="detail-section">
        <h3 class="section-title">关联的下货任务</h3>
        <el-table :data="taskList" stripe border style="width: 100%" max-height="300">
          <el-table-column type="index" align="center" label="序号" width="60" />
          <el-table-column prop="offalTaskCode" align="center" label="任务编号" width="180" />
          <el-table-column prop="createTime" align="center" label="创建时间" width="180" />
          <el-table-column prop="updateTime" align="center" label="更新时间" width="180" />
          <el-table-column label="状态" align="center" width="100">
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button size="mini" type="text" class="text_btn" @click="handleTaskDetail(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 任务详情弹窗 -->
    <el-dialog title="任务详情" :visible.sync="taskDetailVisible" width="80%" :destroy-on-close="true" class="dialog_box">
      <TaskDetail v-if="taskDetailVisible" :offalTaskId="currentTaskId" />
    </el-dialog>
  </el-drawer>
</template>

<script>
import { customerInfo, customerDelete, offalTaskPage } from '@/api/soldManage.js'
import TaskDetail from './TaskDetail.vue'

export default {
  name: 'CustomerDetail',
  components: {
    TaskDetail
  },
  props: {
    detailVisible: {
      type: Boolean,
      default: false
    },
    customerId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      detailInfo: {},
      taskList: [],
      taskDetailVisible: false,
      currentTaskId: ''
    }
  },
  computed: {
    visible: {
      get() {
        return this.detailVisible
      },
      set(val) {
        this.$emit('update:detailVisible', val)
      }
    }
  },
  watch: {
    customerId: {
      handler(newVal) {
        if (newVal) {
          this.getCustomerDetail()
        }
      },
      immediate: true
    },
    detailVisible: {
      handler(newVal) {
        if (newVal && this.customerId) {
          this.getCustomerDetail()
        }
      }
    }
  },
  methods: {
    // 获取客户详情
    async getCustomerDetail() {
      if (!this.customerId) return

      this.loading = true
      try {
        // 获取客户详情
        const res = await customerInfo({
          offalCustomerId: this.customerId
        })
        if (res.success) {
          this.detailInfo = res.result || {}
        } else {
          this.$message.error(res.message || '获取客户详情失败')
        }

        // 获取关联的下货任务
        await this.getTaskList()
      } catch (error) {
        console.error('获取客户详情失败:', error)
        this.$message.error('获取客户详情失败')
      } finally {
        this.loading = false
      }
    },

    // 获取关联的下货任务
    async getTaskList() {
      try {
        const res = await offalTaskPage({
          pageNum: 1,
          pageSize: 100,
          offalCustomerId: this.customerId
        })
        if (res.success) {
          this.taskList = res.result.list || []
        }
      } catch (error) {
        console.error('获取关联任务失败:', error)
      }
    },

    // 编辑
    handleEdit() {
      this.$emit('edit', this.detailInfo)
      this.visible = false
    },

    // 删除
    handleDelete() {
      this.$confirm('此操作将永久删除该客户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await customerDelete({
            offalCustomerId: this.customerId
          })
          if (res.success) {
            this.$message.success('删除成功')
            this.$emit('delete')
            this.visible = false
          } else {
            this.$message.error(res.message || '删除失败')
          }
        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      })
    },

    // 查看任务详情
    handleTaskDetail(row) {
      this.currentTaskId = row.offalTaskId
      this.taskDetailVisible = true
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'info',
        1: 'success',
        2: 'warning',
        3: 'danger'
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待处理',
        1: '已完成',
        2: '进行中',
        3: '已取消'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style scoped lang="scss">
.detail-container {
  padding: 20px;

  .detail-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;
      margin-bottom: 16px;
      padding-bottom: 8px;
    }

    .info-item {
      display: flex;
      margin-bottom: 12px;

      .label {
        color: #86909c;
        font-size: 14px;
        margin-right: 8px;
        min-width: 80px;
      }

      .value {
        flex: 1;
        color: #1d2129;
        font-size: 14px;
      }
    }

    .remark-content {
      padding: 16px;
      background: #f7f8fa;
      border-radius: 6px;
      color: #4e5969;
      font-size: 14px;
      line-height: 1.5;
      min-height: 60px;
    }
  }

  .detail-actions {
    margin-bottom: 30px;
    text-align: center;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e5e6eb;
    .el-button {
      margin: 0 10px;
    }
  }
}
</style>
