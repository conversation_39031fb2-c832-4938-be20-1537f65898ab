<template>
  <div class="weight-record">
    <el-form
      :model="queryParams"
      :inline="true"
      ref="searchForm"
      class="search-form"
    >
      <el-row>
        <el-col :span="5">
          <el-form-item label="批次号" prop="offalTaskCode">
            <el-input
              v-model="queryParams.offalTaskCode"
              placeholder="批次号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="产品名称" prop="productName">
            <el-input
              v-model="queryParams.productName"
              placeholder="产品名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="称重类型" prop="weightingType">
            <el-select
              v-model="weightingType"
              placeholder="全部"
              style="width: 100%"
            >
              <el-option label="全部" value=""></el-option>
              <el-option label="下货" value="下货"></el-option>
              <el-option label="余料" value="余料"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item label="称重时间" prop="weightRange">
            <el-select
              v-model="weightRangeStart"
              placeholder="请选择"
              style="width: 45%; margin-right: 5px"
            >
              <el-option label="请选择" value=""></el-option>
              <el-option label="0-100" value="0-100"></el-option>
              <el-option label="100-500" value="100-500"></el-option>
              <el-option label="500+" value="500+"></el-option>
            </el-select>
            <span style="margin: 0 5px">至</span>
            <el-select
              v-model="weightRangeEnd"
              placeholder="请选择"
              style="width: 45%"
            >
              <el-option label="请选择" value=""></el-option>
              <el-option label="0-100" value="0-100"></el-option>
              <el-option label="100-500" value="100-500"></el-option>
              <el-option label="500+" value="500+"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" style="text-align: right">
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-col>
      </el-row>
    </el-form>

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      stripe
      style="width: 100%"
      v-loading="loading"
      border
      max-height="400"
    >
      <el-table-column type="index" align="center" label="序号" width="60"></el-table-column>
      <el-table-column prop="productSourceCode" align="center" label="批次号" width="120"></el-table-column>
      <el-table-column prop="productName" align="center" label="产品名称"></el-table-column>
      <el-table-column prop="weightingType" align="center" label="称重类型"></el-table-column>
      <el-table-column prop="productSourceCode" align="center" label="任务ID"></el-table-column>
      <el-table-column prop="unitPrice" align="center" label="单价(元/kg)" width="120"></el-table-column>
      <el-table-column prop="grossWeight" align="center" label="毛重(kg)"></el-table-column>
      <el-table-column prop="tareWeight" align="center" label="皮重(kg)"></el-table-column>
      <el-table-column prop="netWeight" align="center" label="净重(kg)"></el-table-column>
      <el-table-column prop="weightAmount" align="center" label="总价格(元)" width="120"></el-table-column>
      <el-table-column prop="updateTime" align="center" label="称重时间" width="180"></el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { offalWeightPage } from '@/api/soldManage.js'

export default {
  name: 'WeightRecord',
  props: {
    offalTaskId: {
      type: String,
      default: ''
    },
    offalTaskCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        offalTaskId: null,
        offalTaskCode: null,
        productName: null,
        offalCustomerId: null,
        customerName: null,
        offalTaskDate: null,
        startTime: null,
        endTime: null
      },
      tableData: [],
      loading: false,
      total: 0,
      weightingType: '',
      weightRangeStart: '',
      weightRangeEnd: ''
    }
  },
  created() {
    this.initParams()
    this.getList()
  },
  methods: {
    // 初始化参数
    initParams() {
      this.queryParams.offalTaskId = this.offalTaskId
      this.queryParams.offalTaskCode = this.offalTaskCode
    },

    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const response = await offalWeightPage(this.queryParams)
        if (response.success) {
          this.tableData = response.result.list || []
          this.total = parseInt(response.result.total) || 0
        } else {
          this.$message.error(response.message || '获取数据失败')
        }
      } catch (error) {
        console.error('获取称重记录失败:', error)
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    // 重置
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        offalTaskId: this.offalTaskId,
        offalTaskCode: this.offalTaskCode,
        productName: null,
        offalCustomerId: null,
        customerName: null,
        offalTaskDate: null,
        startTime: null,
        endTime: null
      }
      this.weightingType = ''
      this.weightRangeStart = ''
      this.weightRangeEnd = ''
      this.getList()
    }
  }
}
</script>

<style scoped lang="scss">
.weight-record {
  .search-form {
    margin-bottom: 20px;
    padding: 16px;
  }
}
</style>
