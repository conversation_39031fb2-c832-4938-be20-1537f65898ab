<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    width="600px"
    :destroy-on-close="true"
    class="dialog_box"
    @close="handleClose"
  >
    <el-form
      ref="customerForm"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="form.customerName"
          placeholder="请输入客户名称"
          maxlength="50"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="customerPhone">
        <el-input
          v-model="form.customerPhone"
          placeholder="请输入联系电话"
          maxlength="11"
        />
      </el-form-item>
      <el-form-item label="联系地址" prop="customerAddress">
        <el-input
          v-model="form.customerAddress"
          placeholder="请输入联系地址"
          maxlength="200"
        />
      </el-form-item>
      <el-form-item label="备注信息" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="500"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { customerAdd, customerUpdate } from '@/api/soldManage.js'

export default {
  name: 'CustomerForm',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    customerData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        offalCustomerId: '',
        customerName: '',
        customerPhone: '',
        customerAddress: '',
        remark: ''
      },
      rules: {
        customerName: [
          { required: true, message: '请输入客户名称', trigger: 'blur' },
          { max: 50, message: '客户名称不能超过50个字符', trigger: 'blur' }
        ],
        customerPhone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        customerAddress: [
          { max: 200, message: '联系地址不能超过200个字符', trigger: 'blur' }
        ],
        remark: [
          { max: 500, message: '备注信息不能超过500个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.dialogVisible
      },
      set(val) {
        this.$emit('update:dialogVisible', val)
      }
    },
    dialogTitle() {
      return this.form.offalCustomerId ? '编辑客户' : '新增客户'
    }
  },
  watch: {
    customerData: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.form = { ...newVal }
        } else {
          this.resetForm()
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 重置表单
    resetForm() {
      this.form = {
        offalCustomerId: '',
        customerName: '',
        customerPhone: '',
        customerAddress: '',
        remark: ''
      }
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.customerForm.validate(async (valid) => {
        if (!valid) return
        
        try {
          let response
          if (this.form.offalCustomerId) {
            // 编辑
            response = await customerUpdate(this.form)
          } else {
            // 新增
            const { offalCustomerId, ...addData } = this.form
            response = await customerAdd(addData)
          }
          
          if (response.success) {
            this.$message.success(this.form.offalCustomerId ? '编辑成功' : '新增成功')
            this.$emit('success')
            this.handleClose()
          } else {
            this.$message.error(response.message || '操作失败')
          }
        } catch (error) {
          console.error('操作失败:', error)
          this.$message.error('操作失败')
        }
      })
    },
    
    // 关闭弹窗
    handleClose() {
      this.$refs.customerForm && this.$refs.customerForm.resetFields()
      this.visible = false
    }
  }
}
</script>
</template>
