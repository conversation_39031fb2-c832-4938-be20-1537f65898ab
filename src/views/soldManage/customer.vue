<template>
    <div class="app-container">
        <el-card shadow="never" class="box-card form-card mb10">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
                <el-row class="form_row">
                    <el-row class="form_col">
                        <el-form-item prop="customerName">
                            <el-input v-model="queryParams.customerName" placeholder="客户名称" clearable
                                @keyup.enter.native="handleQuery" />
                        </el-form-item>
                        <el-form-item prop="customerPhone">
                            <el-input v-model="queryParams.customerPhone" placeholder="联系电话" clearable
                                @keyup.enter.native="handleQuery" />
                        </el-form-item>
                        <el-form-item prop="dateRange">
                            <el-date-picker v-model="dateRange" type="datetimerange" range-separator="至"
                                start-placeholder="更新时间" end-placeholder="请选择" value-format="yyyy-MM-dd HH:mm:ss"
                                @change="handleQuery" />
                        </el-form-item>
                    </el-row>
                </el-row>
                <el-row>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-row>
            </el-form>
        </el-card>

        <el-card shadow="never" class="table_box">
            <el-row :gutter="10" class="mb8 form_btn">
                <el-col :span="24" class="fend">
                    <el-button class="default_btn" size="mini" icon="el-icon-plus" @click="handleAdd">新建</el-button>
                </el-col>
            </el-row>

            <div :style="{ height: tableHeight + 'px' }">
                <el-table :data="tableData" stripe style="width: 100%" v-loading="loading" :max-height="tableHeight"
                    border>
                    <el-table-column type="index" width="55" align="center" label="序号" />
                    <el-table-column show-overflow-tooltip v-for="(item, index) in tableColumn" :key="index"
                        :align="item.align" :sortable="item.sortable" :prop="item.prop" :label="item.label" />
                    <el-table-column label="操作" align="center">
                        <template slot-scope="scope">
                            <el-button class="text_btn" size="mini" icon="el-icon-view" @click="handleDetail(scope.row)"
                                type="text">详情</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" @pagination="getList" />
        </el-card>

        <!-- 新增/编辑 -->
        <CustomerForm
            :dialog-visible.sync="dialogVisible"
            :customer-data="currentCustomer"
            @success="handleFormSuccess"
        />

        <!-- 详情 -->
        <CustomerDetail
            :detail-visible.sync="detailVisible"
            :customer-id="currentCustomerId"
            @edit="handleEditFromDetail"
            @delete="handleDeleteSuccess"
        />
    </div>
</template>

<script>
import { customerPage } from '@/api/soldManage.js'
import { tableUi } from "@/utils/mixin/tableUi.js"
import CustomerForm from './components/CustomerForm.vue'
import CustomerDetail from './components/CustomerDetail.vue'

export default {
    name: 'Customer',
    mixins: [tableUi],
    components: {
        CustomerForm,
        CustomerDetail
    },
    data() {
        return {
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                customerName: '',
                customerPhone: '',
                startTime: '',
                endTime: '',
                searchValue: ''
            },
            dateRange: [],

            // 表格数据
            tableData: [],
            loading: true,
            total: 0,
            tableColumn: [
                { check: true, prop: "customerName", label: "客户名称", align: 'center' },
                { check: true, prop: "customerPhone", label: "联系电话", align: 'center' },
                { check: true, prop: "customerAddress", label: "客户地址", align: 'center' },
                { check: true, prop: "remark", label: "备注", align: 'center' },
                { check: true, prop: "createBy", label: "创建人", align: 'center' },
                { check: true, prop: "createTime", label: "创建时间", align: 'center', sortable: true },
                { check: true, prop: "updateBy", label: "更新人", align: 'center' },
                { check: true, prop: "updateTime", label: "更新时间", align: 'center', sortable: true }
            ],

            // 弹窗
            dialogVisible: false,
            currentCustomer: {},

            // 详情
            detailVisible: false,
            currentCustomerId: ''
        }
    },

    created() {
        this.getList()
    },

    methods: {
        async getList() {
            this.loading = true
            try {
                const res = await customerPage(this.queryParams)
                if (res.success) {
                    this.tableData = res.result.list || []
                    this.total = parseInt(res.result.total) || 0
                } else {
                    this.$message.error(res.message || '获取客户列表失败')
                }
            } catch (error) {
                console.error('获取客户列表失败:', error)
                this.$message.error('获取客户列表失败')
            } finally {
                this.loading = false
            }
        },

        // 搜索
        handleQuery() {
            this.queryParams.pageNum = 1
            if (this.dateRange && this.dateRange.length === 2) {
                this.queryParams.startTime = this.dateRange[0]
                this.queryParams.endTime = this.dateRange[1]
            } else {
                this.queryParams.startTime = ''
                this.queryParams.endTime = ''
            }
            this.getList()
        },

        // 重置搜索
        resetQuery() {
            this.$refs.queryForm.resetFields()
            this.dateRange = []
            this.handleQuery()
        },

        // 重置表单
        reset() {
            this.$refs.queryForm.resetFields()
        },

        // 新增
        handleAdd() {
            this.currentCustomer = {}
            this.dialogVisible = true
        },

        // 查看详情
        handleDetail(row) {
            this.currentCustomerId = row.offalCustomerId
            this.detailVisible = true
        },

        handleFormSuccess() {
            this.getList()
        },

        // 详情页编辑
        handleEditFromDetail(customerData) {
            this.currentCustomer = customerData
            this.dialogVisible = true
        },

        // 删除
        handleDeleteSuccess() {
            this.getList()
        },
    }
}
</script>

<style scoped lang="scss">
</style>
