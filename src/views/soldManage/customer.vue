<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索区域 -->
      <div class="filter-container">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="queryParams.customerName"
              placeholder="客户名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="queryParams.customerPhone"
              placeholder="联系电话"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="更新时间"
              end-placeholder="请选择"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="handleQuery"
            />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 操作按钮 -->
      <div class="mb20">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      </div>

      <!-- 表格 -->
      <div class="table-container">
        <el-table
          :data="tableData"
          v-loading="loading"
          stripe
          border
          style="width: 100%"
        >
          <el-table-column type="index" align="center" label="序号" width="60" />
          <el-table-column prop="customerName" align="center" label="客户名称" />
          <el-table-column prop="customerPhone" align="center" label="联系电话" />
          <el-table-column prop="remark" align="center" label="备注" show-overflow-tooltip />
          <el-table-column prop="createTime" align="center" label="创建时间" width="180" />
          <el-table-column prop="createBy" align="center" label="创建人" />
          <el-table-column prop="updateTime" align="center" label="更新时间" width="180" />
          <el-table-column prop="updateBy" align="center" label="更新人" />
          <el-table-column label="操作" align="center" width="120" fixed="right">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-view"
                size="mini"
                type="text"
                class="text_btn"
                @click="handleDetail(scope.row)"
              >详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      :destroy-on-close="true"
      class="dialog_box"
      @close="handleDialogClose"
    >
      <el-form
        ref="customerForm"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="客户名称" prop="customerName">
          <el-input
            v-model="form.customerName"
            placeholder="请输入客户名称"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item label="联系电话" prop="customerPhone">
          <el-input
            v-model="form.customerPhone"
            placeholder="请输入联系电话"
            maxlength="11"
          />
        </el-form-item>
        <el-form-item label="联系地址" prop="customerAddress">
          <el-input
            v-model="form.customerAddress"
            placeholder="请输入联系地址"
            maxlength="200"
          />
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
            maxlength="500"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </div>
    </el-dialog>

    <!-- 详情抽屉 -->
    <el-drawer
      title="客户信息"
      :visible.sync="detailVisible"
      size="60%"
      :destroy-on-close="true"
      class="drawer_box"
    >
      <div class="detail-container" v-loading="detailLoading">
        <!-- 客户基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">客户信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="label">客户名称：</span>
                <span class="value">{{ detailInfo.customerName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="label">联系电话：</span>
                <span class="value">{{ detailInfo.customerPhone || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="info-item">
                <span class="label">客户地址：</span>
                <span class="value">{{ detailInfo.customerAddress || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ detailInfo.createTime || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="label">更新时间：</span>
                <span class="value">{{ detailInfo.updateTime || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="label">创建人：</span>
                <span class="value">{{ detailInfo.createBy || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="label">更新人：</span>
                <span class="value">{{ detailInfo.updateBy || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 备注信息 -->
        <div class="detail-section">
          <h3 class="section-title">备注信息</h3>
          <div class="remark-content">
            {{ detailInfo.remark || '-' }}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="detail-actions">
          <el-button type="primary" @click="handleEdit">编辑</el-button>
          <el-button type="danger" @click="handleDelete">删除</el-button>
        </div>

        <!-- 关联的下货任务 -->
        <div class="detail-section">
          <h3 class="section-title">关联的下货任务</h3>
          <el-table
            :data="taskList"
            stripe
            border
            style="width: 100%"
            max-height="300"
          >
            <el-table-column type="index" align="center" label="序号" width="60" />
            <el-table-column prop="offalTaskCode" align="center" label="任务编号" width="180" />
            <el-table-column prop="createTime" align="center" label="创建时间" width="180" />
            <el-table-column prop="updateTime" align="center" label="更新时间" width="180" />
            <el-table-column label="状态" align="center" width="100">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="100">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  class="text_btn"
                  @click="handleTaskDetail(scope.row)"
                >查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-drawer>

    <!-- 任务详情弹窗 -->
    <el-dialog
      title="任务详情"
      :visible.sync="taskDetailVisible"
      width="80%"
      :destroy-on-close="true"
      class="dialog_box"
    >
      <TaskDetail
        v-if="taskDetailVisible"
        :offalTaskId="currentTaskId"
      />
    </el-dialog>
  </div>
</template>

<script>
import { customerPage, customerInfo, customerAdd, customerUpdate, customerDelete, offalTaskPage } from '@/api/soldManage.js'
import { tableUi } from "@/utils/mixin/tableUi.js"
import TaskDetail from './components/TaskDetail.vue'

export default {
  name: 'Customer',
  mixins: [tableUi],
  components: {
    TaskDetail
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: '',
        customerPhone: '',
        startTime: '',
        endTime: '',
        searchValue: ''
      },
      dateRange: [],

      // 表格数据
      tableData: [],
      loading: false,
      total: 0,

      // 弹窗相关
      dialogVisible: false,
      dialogTitle: '',
      form: {
        offalCustomerId: '',
        customerName: '',
        customerPhone: '',
        customerAddress: '',
        remark: ''
      },

      // 详情相关
      detailVisible: false,
      detailLoading: false,
      detailInfo: {},
      taskList: [],

      // 任务详情
      taskDetailVisible: false,
      currentTaskId: '',

      // 表单验证规则
      rules: {
        customerName: [
          { required: true, message: '请输入客户名称', trigger: 'blur' },
          { max: 50, message: '客户名称不能超过50个字符', trigger: 'blur' }
        ],
        customerPhone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        customerAddress: [
          { max: 200, message: '联系地址不能超过200个字符', trigger: 'blur' }
        ],
        remark: [
          { max: 500, message: '备注信息不能超过500个字符', trigger: 'blur' }
        ]
      }
    }
  },

  created() {
    this.getList()
  },

  methods: {
    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const response = await customerPage(this.queryParams)
        if (response.success) {
          this.tableData = response.result.list || []
          this.total = parseInt(response.result.total) || 0
        } else {
          this.$message.error(response.message || '获取客户列表失败')
        }
      } catch (error) {
        console.error('获取客户列表失败:', error)
        this.$message.error('获取客户列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.startTime = this.dateRange[0]
        this.queryParams.endTime = this.dateRange[1]
      } else {
        this.queryParams.startTime = ''
        this.queryParams.endTime = ''
      }
      this.getList()
    },

    // 重置搜索
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        customerName: '',
        customerPhone: '',
        startTime: '',
        endTime: '',
        searchValue: ''
      }
      this.dateRange = []
      this.getList()
    },

    // 新增
    handleAdd() {
      this.dialogTitle = '新增客户'
      this.form = {
        offalCustomerId: '',
        customerName: '',
        customerPhone: '',
        customerAddress: '',
        remark: ''
      }
      this.dialogVisible = true
    },

    // 编辑
    handleEdit() {
      this.dialogTitle = '编辑客户'
      this.form = { ...this.detailInfo }
      this.dialogVisible = true
      this.detailVisible = false
    },

    // 查看详情
    async handleDetail(row) {
      this.detailVisible = true
      this.detailLoading = true
      try {
        // 获取客户详情
        const response = await customerInfo({
          offalCustomerId: row.offalCustomerId
        })
        if (response.success) {
          this.detailInfo = response.result || {}
        } else {
          this.$message.error(response.message || '获取客户详情失败')
        }

        // 获取关联的下货任务
        await this.getTaskList(row.offalCustomerId)
      } catch (error) {
        console.error('获取客户详情失败:', error)
        this.$message.error('获取客户详情失败')
      } finally {
        this.detailLoading = false
      }
    },

    // 获取关联的下货任务
    async getTaskList(customerId) {
      try {
        const response = await offalTaskPage({
          pageNum: 1,
          pageSize: 100,
          offalCustomerId: customerId
        })
        if (response.success) {
          this.taskList = response.result.list || []
        }
      } catch (error) {
        console.error('获取关联任务失败:', error)
      }
    },

    // 删除
    handleDelete() {
      this.$confirm('此操作将永久删除该客户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await customerDelete({
            offalCustomerId: this.detailInfo.offalCustomerId
          })
          if (response.success) {
            this.$message.success('删除成功')
            this.detailVisible = false
            this.getList()
          } else {
            this.$message.error(response.message || '删除失败')
          }
        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      })
    },

    // 提交表单
    handleSubmit() {
      this.$refs.customerForm.validate(async (valid) => {
        if (!valid) return

        try {
          let response
          if (this.form.offalCustomerId) {
            // 编辑
            response = await customerUpdate(this.form)
          } else {
            // 新增
            const { offalCustomerId, ...addData } = this.form
            response = await customerAdd(addData)
          }

          if (response.success) {
            this.$message.success(this.form.offalCustomerId ? '编辑成功' : '新增成功')
            this.dialogVisible = false
            this.getList()
            // 如果是编辑，更新详情信息
            if (this.form.offalCustomerId && this.detailVisible) {
              this.handleDetail({ offalCustomerId: this.form.offalCustomerId })
            }
          } else {
            this.$message.error(response.message || '操作失败')
          }
        } catch (error) {
          console.error('操作失败:', error)
          this.$message.error('操作失败')
        }
      })
    },

    // 关闭弹窗
    handleDialogClose() {
      this.$refs.customerForm && this.$refs.customerForm.resetFields()
    },

    // 查看任务详情
    handleTaskDetail(row) {
      this.currentTaskId = row.offalTaskId
      this.taskDetailVisible = true
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'info',
        1: 'success',
        2: 'warning',
        3: 'danger'
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待处理',
        1: '已完成',
        2: '进行中',
        3: '已取消'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.mb20 {
  margin-bottom: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.detail-container {
  padding: 20px;

  .detail-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e5e6eb;
    }

    .info-item {
      display: flex;
      margin-bottom: 12px;

      .label {
        color: #86909c;
        font-size: 14px;
        margin-right: 8px;
        min-width: 80px;
      }

      .value {
        flex: 1;
        color: #1d2129;
        font-size: 14px;
      }
    }

    .remark-content {
      padding: 16px;
      background: #f7f8fa;
      border-radius: 6px;
      color: #4e5969;
      font-size: 14px;
      line-height: 1.5;
      min-height: 60px;
    }
  }

  .detail-actions {
    margin-bottom: 30px;
    text-align: center;

    .el-button {
      margin: 0 10px;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
