<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-card shadow="never" class="box-card form-card mb10">
      <el-form
        :model="queryParams"
        size="small"
        :inline="true"
        ref="formBox"
        class="form_box"
      >
        <el-row class="form_row">
          <el-row class="form_col">
            <el-form-item prop="offalTaskCode">
              <el-input
                v-model="queryParams.offalTaskCode"
                placeholder="批次号"
              />
            </el-form-item>
            <el-form-item prop="customerName">
              <el-input
                v-model="queryParams.customerName"
                placeholder="客户名称"
              />
            </el-form-item>
            <el-form-item prop="updateTime">
              <el-date-picker
                v-model="updateTime"
                type="datetimerange"
                range-separator="至"
                start-placeholder="请选择"
                end-placeholder="请选择"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="handleTimeChange"
              />
            </el-form-item>
          </el-row>
        </el-row>
        <el-row>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card shadow="never" class="table_box">
      <el-row :gutter="10" class="mb8 form_btn">
        <el-col class="fend">
          <el-button class="default_btn" size="mini"  @click="handleWeight">称重记录</el-button>
        </el-col>
      </el-row>

      <el-table
        :data="tableData"
        stripe
        style="width: 100%"
        v-loading="loading"
        border
        :height="tableHeight"
      >
        <el-table-column type="index" align="center" label="序号" width="60"></el-table-column>
        <el-table-column prop="offalTaskCode" align="center" label="任务编号"></el-table-column>
        <el-table-column prop="customerName" align="center" label="客户名称"></el-table-column>
        <el-table-column prop="createBy" align="center" label="创建人"></el-table-column>
        <el-table-column prop="createTime" align="center" label="创建时间"></el-table-column>
        <el-table-column prop="updateTime" align="center" label="更新时间"></el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template slot-scope="scope">
            <el-button type="text" class="edit_text_btn" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 称重记录弹窗 -->
    <el-dialog
      title="称重记录"
      :visible.sync="weightDialogVisible"
      width="80%"
      :destroy-on-close="true"
      class="dialog_box"
    >
      <WeightRecord
        v-if="weightDialogVisible"
        :offalTaskId="currentTaskId"
        :offalTaskCode="currentTaskCode"
      />
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog
      title="查看详情"
      :visible.sync="detailDialogVisible"
      width="80%"
      :destroy-on-close="true"
      class="dialog_box"
    >
      <TaskDetail
        v-if="detailDialogVisible"
        :offalTaskId="currentTaskId"
      />
    </el-dialog>
  </div>
</template>
<script>
import { offalTaskPage } from '@/api/soldManage.js'
import { tableUi } from "@/utils/mixin/tableUi.js"
import WeightRecord from './components/WeightRecord.vue'
import TaskDetail from './components/TaskDetail.vue'

export default {
  name: 'OffalManage',
  mixins: [tableUi],
  components: {
    WeightRecord,
    TaskDetail
  },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: '',
        offalTaskCode: '',
        startTime: '',
        endTime: ''
      },
      tableData: [],
      loading: false,
      total: 0,
      updateTime: [],
      weightDialogVisible: false,
      detailDialogVisible: false,
      currentTaskId: '',
      currentTaskCode: ''
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const response = await offalTaskPage(this.queryParams)
        if (response.success) {
          this.tableData = response.result.list || []
          this.total = parseInt(response.result.total) || 0
        } else {
          this.$message.error(response.message || '获取数据失败')
        }
      } catch (error) {
        console.error('获取列表失败:', error)
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    // 重置
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        customerName: '',
        offalTaskCode: '',
        startTime: '',
        endTime: ''
      }
      this.updateTime = []
      this.getList()
    },

    // 时间范围变化
    handleTimeChange(val) {
      if (val && val.length === 2) {
        this.queryParams.startTime = val[0]
        this.queryParams.endTime = val[1]
      } else {
        this.queryParams.startTime = ''
        this.queryParams.endTime = ''
      }
    },

    // 查看详情
    handleDetail(row) {
      this.currentTaskId = row.offalTaskId
      this.detailDialogVisible = true
    },

    // 称重记录
    handleWeight(row) {
      this.currentTaskId = row.offalTaskId
      this.currentTaskCode = row.offalTaskCode
      this.weightDialogVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  .form_btn {
    .fend {
      text-align: right;
    }
  }
}
</style>
